import './style.css';
import './app.css';

import {
    Greet,
    GetAccounts,
    AddAccount,
    DeleteAccount,
    UpdateAccount,
    LoginSteam,
    LaunchGame,
    GetAccountStats,
    ImportAccounts,
    CheckAccountBanStatus,
    GetAccountsPaginated,
    SetAccountSteamID,
    GetConfig,
    UpdateConfig,
    TriggerSteamIDCheck,
    GetLogs,
    ClearLogs,
    GetInstalledSteamGames
} from '../wailsjs/go/main/App';

// 全局状态
let accounts = [];
let editingAccount = null;
let currentPage = 1;
const pageSize = 10;
let totalPages = 0;
let settingSteamIdAccountId = null;
let logs = [];
let showLogs = false;
let logPollingInterval = null;
let accountPollingInterval = null;

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    document.querySelector('#app').innerHTML = `
        <div class="container">
            <header class="header">
                <h1>Steam账号管理工具</h1>
                <div class="stats" id="stats">
                    <span>总计: 0 | 正常: 0 | 封禁: 0 | 未知: 0</span>
                </div>
            </header>

            <div class="toolbar">
                <button class="btn btn-primary" onclick="showAddAccountModal()">添加账号</button>
                <button class="btn btn-secondary" onclick="showImportModal()">批量导入</button>
                <button class="btn btn-info" onclick="refreshAccounts()">刷新列表</button>
                <button class="btn btn-warning" onclick="launchGameOnly()">启动游戏</button>
                <button class="btn btn-info" id="logsToggleBtn" onclick="toggleLogs()">显示日志</button>
                <button class="btn btn-secondary" onclick="showConfigModal()">设置</button>
            </div>

            <div class="account-list" id="accountList">
                <div class="loading">加载中...</div>
            </div>

            <div class="pagination" id="pagination">
                <!-- 分页控件将在这里生成 -->
            </div>

            <!-- 日志区域 -->
            <div class="logs-container" id="logsContainer" style="display: none;">
                <div class="logs-header">
                    <h3>操作日志</h3>
                    <button class="btn btn-sm btn-secondary" onclick="clearLogs()">清空日志</button>
                </div>
                <div class="logs-content" id="logsContent">
                    <div class="log-empty">暂无日志</div>
                </div>
            </div>
        </div>

        <!-- 右上角斜角水印 -->
        <div class="watermark-banner">
            <span>新娱乐群</span>
        </div>
        
        <!-- 添加/编辑账号模态框 -->
        <div id="accountModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('accountModal')">&times;</span>
                <h2 id="modalTitle">添加账号</h2>
                <form id="accountForm">
                    <div class="form-group">
                        <label>用户名:</label>
                        <input type="text" id="username" required>
                    </div>
                    <div class="form-group">
                        <label>密码:</label>
                        <input type="password" id="password" required>
                    </div>
                    <div class="form-group">
                        <label>备注:</label>
                        <input type="text" id="notes">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary" id="submitBtn">添加</button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('accountModal')">取消</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 导入账号模态框 -->
        <div id="importModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('importModal')">&times;</span>
                <h2>批量导入账号</h2>
                <div class="form-group">
                    <label>账号列表 (每行一个账号信息):</label>
                    <textarea id="importText" rows="10" placeholder="支持格式：&#10;1. #账号----密码&#10;2. 账号----密码&#10;3. 账号 密码 (空格分隔)&#10;4. 账号	密码 (Tab分隔)&#10;&#10;注释行以 # 或 // 开头"></textarea>
                    <div class="help-text">
                        <p><strong>支持的格式：</strong></p>
                        <p>• #账号----密码</p>
                        <p>• 账号----密码</p>
                        <p>• 账号 密码 (空格分隔)</p>
                        <p>• 账号	密码 (Tab分隔)</p>
                        <p>• 以 # 或 // 开头的行将被视为注释</p>
                    </div>
                </div>
                <div class="form-actions">
                    <button class="btn btn-primary" onclick="importAccounts()">导入</button>
                    <button class="btn btn-secondary" onclick="closeModal('importModal')">取消</button>
                </div>
            </div>
        </div>

        <!-- 设置SteamID模态框 -->
        <div id="steamIdModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('steamIdModal')">&times;</span>
                <h2>设置SteamID</h2>
                <div class="form-group">
                    <label>SteamID (17位数字，以76561开头):</label>
                    <input type="text" id="steamIdInput" placeholder="例如: *****************" maxlength="17">
                    <div class="help-text">
                        <p>如何获取SteamID:</p>
                        <p>1. 访问 <a href="https://steamid.io/" target="_blank">steamid.io</a></p>
                        <p>2. 输入您的Steam个人资料链接或用户名</p>
                        <p>3. 复制 "SteamID64" 的值</p>
                    </div>
                </div>
                <div class="form-actions">
                    <button class="btn btn-primary" onclick="saveSteamID()">保存</button>
                    <button class="btn btn-secondary" onclick="closeModal('steamIdModal')">取消</button>
                </div>
            </div>
        </div>

        <!-- 配置模态框 -->
        <div id="configModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="closeModal('configModal')">&times;</span>
                <h2>应用设置</h2>

                <!-- 游戏启动设置 -->
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="autoLaunchGame"> 🎮 登录后自动启动游戏
                    </label>
                    <div class="help-text">启用后，Steam登录成功后会自动启动选择的游戏</div>
                </div>
                <div class="form-group">
                    <label for="gameSelect">🎯 选择启动游戏:</label>
                    <select id="gameSelect" style="width: 100%; margin-top: 5px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="">正在加载游戏列表...</option>
                    </select>
                    <div class="help-text">选择Steam登录后要启动的游戏，默认为PUBG</div>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="autoCloseGame"> 🔄 登录前自动关闭游戏
                    </label>
                    <div class="help-text">启用后，登录新账户前会自动关闭正在运行的游戏进程</div>
                </div>

                <!-- 安妮程序设置 -->
                <div class="form-group" style="border-top: 1px solid #eee; padding-top: 15px; margin-top: 15px;">
                    <label>
                        <input type="checkbox" id="annieEnabled"> 🤖 启用安妮程序
                    </label>
                    <div class="help-text">启用后，Steam登录成功后会先启动安妮程序，自动处理弹窗，完成后再启动游戏</div>
                </div>
                <div class="form-group" id="anniePasswordGroup" style="margin-left: 20px; margin-top: 10px;">
                    <label for="annieRarPassword">🔐 RAR文件密码:</label>
                    <input type="text" id="annieRarPassword" placeholder="请输入an.rar的解压密码（默认：aaa111）" style="width: 100%; margin-top: 5px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <div class="help-text" style="margin-top: 5px; font-size: 12px; color: #666;">
                        💡 请确保程序目录下存在an.rar文件且系统已安装WinRAR
                    </div>
                      <div class="help-text" style="margin-top: 5px; font-size: 12px; color: #666;">
                        💡 请确保安妮程序的【自动登录】已开启
                    </div>
                </div>


        
                <div class="form-actions">
                    <button class="btn btn-primary" onclick="saveConfig()">保存</button>
                    <button class="btn btn-secondary" onclick="closeModal('configModal')">取消</button>
                </div>
            </div>
        </div>
    `;

    // 绑定事件
    bindEvents();
    
    // 加载初始数据
    loadAccounts();
    loadStats();

    // 启动日志轮询
    startLogPolling();

    // 不再使用定时轮询，改为事件驱动更新
    // startAccountPolling();
}

function bindEvents() {
    // 账号表单提交
    document.getElementById('accountForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveAccount();
    });

    // 安妮程序启用状态变化时控制密码输入框显示
    document.addEventListener('change', function(e) {
        if (e.target && e.target.id === 'annieEnabled') {
            const passwordGroup = document.getElementById('anniePasswordGroup');
            if (passwordGroup) {
                passwordGroup.style.display = e.target.checked ? 'block' : 'none';
            }
        }
    });
}

// 加载账号列表
async function loadAccounts() {
    try {
        // 使用分页API
        const response = await GetAccountsPaginated(currentPage, pageSize);
        if (response && response.accounts) {
            accounts = response.accounts || [];
            totalPages = response.totalPages || 0;
            currentPage = response.currentPage || 1;
        } else {
            // 如果分页API返回格式不对，使用原来的API
            accounts = await GetAccounts();
            totalPages = Math.ceil(accounts.length / pageSize);
        }

        renderAccountList();
        renderPagination();
    } catch (error) {
        console.error('加载账号列表失败:', error);
        showMessage('加载账号列表失败: ' + error, 'error');

        // 如果分页API失败，尝试使用原来的API
        try {
            accounts = await GetAccounts();
            totalPages = Math.ceil(accounts.length / pageSize);
            renderAccountList();
            renderPagination();
        } catch (fallbackError) {
            console.error('备用API也失败:', fallbackError);
            // 显示空列表
            accounts = [];
            totalPages = 0;
            renderAccountList();
            renderPagination();
        }
    }
}

// 渲染账号列表
function renderAccountList() {
    const listElement = document.getElementById('accountList');

    if (accounts.length === 0) {
        listElement.innerHTML = '<div class="empty">暂无账号</div>';
        return;
    }

    // 调试：打印账号数据
    console.log('渲染账号列表:', accounts);
    accounts.forEach(account => {
        if (account.isLoggedIn) {
            console.log('发现登录账号:', account.username, 'isLoggedIn:', account.isLoggedIn);
        }
    });

    // 表格形式显示
    const tableHtml = `
        <table class="account-table">
            <thead>
                <tr>
                    <th width="60">ID</th>
                    <th>账号</th>
                    <th>状态</th>
                    <th>最后登录</th>
                    <th>备注</th>
                    <th>SteamID</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                ${accounts.map((account, index) => {
                    const loggedInClass = account.isLoggedIn ? 'logged-in-row' : '';
                    const loggedInIcon = account.isLoggedIn ? '🔥 ' : '';
                    const anyAccountLoggingIn = isAnyAccountLoggingIn();

                    // 按钮状态逻辑
                    let loginButtonText = '登录';
                    let loginButtonClass = 'btn-primary';
                    let loginButtonDisabled = '';
                    let loginButtonFinalText = '登录';

                    // if (account.isLoggedIn) {
                    //     loginButtonText = '已登录';
                    //     loginButtonClass = 'btn-success';
                    //     loginButtonDisabled = 'disabled';
                    //     loginButtonFinalText = '已登录';
                    // } else 
                        
                        if (account.isLoggingIn) {
                        loginButtonText = '登录中...';
                        loginButtonClass = 'btn-warning';
                        loginButtonDisabled = 'disabled';
                        loginButtonFinalText = '登录中...';
                    } else if (anyAccountLoggingIn) {
                        loginButtonText = '等待中';
                        loginButtonClass = 'btn-secondary';
                        loginButtonDisabled = 'disabled';
                        loginButtonFinalText = '等待中';
                    }

                    // 格式化最后登录时间
                    const lastLoginText = account.lastLogin ?
                        new Date(account.lastLogin).toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                        }) : '从未登录';

                    // 调试：打印每个账号的登录状态
                    if (account.isLoggedIn) {
                        console.log(`账号 ${account.username} 应该高亮显示，CSS类: ${loggedInClass}`);
                    }

                    return `
                        <tr class="account-row ${loggedInClass}" ${account.isLoggedIn ? 'title="当前登录账号"' : ''} data-logged-in="${account.isLoggedIn}">
                            <td class="index-cell">${account.id}</td>
                            <td class="username-cell">
                                ${loggedInIcon}${account.username}
                                ${account.isLoggedIn ? '<span class="login-badge">当前登录</span>' : ''}
                            </td>
                            <td class="status-cell">
                                <span class="status-badge status-${account.status.toLowerCase().replace(/\s+/g, '')}">${account.status}</span>
                            </td>
                            <td class="time-cell">${lastLoginText}</td>
                            <td class="notes-cell">${account.notes || '-'}</td>
                            <td class="steamid-cell">
                                ${account.steamId && account.steamId !== 'FAILED' ?
                                    `<span class="steamid-text">${account.steamId}</span>` :
                                    account.steamId === 'FAILED' ?
                                    `<span class="steamid-failed">获取失败 <button class="btn btn-xs btn-link" onclick="retrySteamID(${account.id})">重试</button></span>` :
                                    `<span class="steamid-status">自动获取中...</span>`
                                }
                            </td>
                            <td class="actions-cell">
                                <button class="btn btn-sm ${loginButtonClass}"
                                        onclick="loginAccountById(${account.id})"
                                        ${loginButtonDisabled}
                                        data-account-id="${account.id}"
                                        title="${anyAccountLoggingIn && !account.isLoggingIn && !account.isLoggedIn ? '有账号正在登录中，请等待' : ''}">
                                    ${loginButtonFinalText}
                                </button>
                                <button class="btn btn-sm btn-info" onclick="editAccount(${account.id})">编辑</button>
                                <button class="btn btn-sm btn-warning" onclick="checkBanStatus(${account.id})"
                                        ${!account.steamId ? 'disabled title="需要先获取SteamID"' : ''}>检查</button>
                                <button class="btn btn-sm btn-danger" onclick="deleteAccount(${account.id})">删除</button>
                            </td>
                        </tr>
                    `;
                }).join('')}
            </tbody>
        </table>
    `;

    listElement.innerHTML = tableHtml;
}

// 渲染分页
function renderPagination() {
    const paginationElement = document.getElementById('pagination');

    if (totalPages <= 1) {
        paginationElement.innerHTML = '';
        return;
    }

    let html = '<div class="pagination-controls">';

    // 上一页
    if (currentPage > 1) {
        html += `<button class="btn btn-sm" onclick="changePage(${currentPage - 1})">上一页</button>`;
    }

    // 计算显示的页码范围（最多显示5个页码）
    const maxVisiblePages = 5;
    let startPage, endPage;

    if (totalPages <= maxVisiblePages) {
        // 总页数不超过5页，显示所有页码
        startPage = 1;
        endPage = totalPages;
    } else {
        // 总页数超过5页，需要智能显示
        const halfVisible = Math.floor(maxVisiblePages / 2);

        if (currentPage <= halfVisible + 1) {
            // 当前页在前面，显示前5页
            startPage = 1;
            endPage = maxVisiblePages;
        } else if (currentPage >= totalPages - halfVisible) {
            // 当前页在后面，显示后5页
            startPage = totalPages - maxVisiblePages + 1;
            endPage = totalPages;
        } else {
            // 当前页在中间，以当前页为中心显示5页
            startPage = currentPage - halfVisible;
            endPage = currentPage + halfVisible;
        }
    }

    // 如果不是从第1页开始，显示第1页和省略号
    if (startPage > 1) {
        html += `<button class="btn btn-sm" onclick="changePage(1)">1</button>`;
        if (startPage > 2) {
            html += `<span class="pagination-ellipsis">...</span>`;
        }
    }

    // 显示页码范围
    for (let i = startPage; i <= endPage; i++) {
        if (i === currentPage) {
            html += `<button class="btn btn-sm btn-primary">${i}</button>`;
        } else {
            html += `<button class="btn btn-sm" onclick="changePage(${i})">${i}</button>`;
        }
    }

    // 如果不是到最后一页，显示省略号和最后一页
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<span class="pagination-ellipsis">...</span>`;
        }
        html += `<button class="btn btn-sm" onclick="changePage(${totalPages})">${totalPages}</button>`;
    }

    // 下一页
    if (currentPage < totalPages) {
        html += `<button class="btn btn-sm" onclick="changePage(${currentPage + 1})">下一页</button>`;
    }

    // 显示页码信息
    html += `<span class="pagination-info">第 ${currentPage} 页，共 ${totalPages} 页</span>`;

    html += '</div>';
    paginationElement.innerHTML = html;
}

// 切换页面
function changePage(page) {
    currentPage = page;
    loadAccounts();
}

// 检查封禁状态
async function checkBanStatus(accountId) {
    // 检查账号是否有SteamID
    const account = accounts.find(acc => acc.id === accountId);
    if (!account || !account.steamId) {
        showMessage('请先设置该账号的SteamID', 'error');
        addLog('ERROR', `账号 ${account ? account.username : 'Unknown'} 缺少SteamID，无法检查封禁状态`);
        return;
    }

    try {
        addLog('INFO', `开始检查账号 ${account.username} 的封禁状态...`);
        showMessage('正在检查封禁状态，请稍候...', 'info');

        await CheckAccountBanStatus(accountId);

        addLog('SUCCESS', `已提交 ${account.username} 的封禁状态检查请求`);
        showMessage('封禁状态检查已启动，正在调用Steam API...', 'success');

        // 5秒后自动刷新查看结果
        setTimeout(() => {
            loadAccounts();
            addLog('INFO', `${account.username} 的封禁状态检查完成`);
            showMessage('封禁状态检查完成，请查看结果', 'info');
        }, 5000);
    } catch (error) {
        console.error('检查封禁状态失败:', error);
        addLog('ERROR', `检查 ${account.username} 封禁状态失败: ${error}`);
        showMessage('检查封禁状态失败: ' + error, 'error');
    }
}

// 加载统计信息
async function loadStats() {
    try {
        const stats = await GetAccountStats();
        const statsElement = document.getElementById('stats');
        statsElement.innerHTML = `
            <span>总计: ${stats.total || 0} | 正常: ${stats.normal || 0} | 封禁: ${stats.banned || 0} | 未知: ${stats.unknown || 0}</span>
        `;
    } catch (error) {
        console.error('获取统计信息失败:', error);
    }
}

// 显示添加账号模态框
function showAddAccountModal() {
    editingAccount = null;
    document.getElementById('modalTitle').textContent = '添加账号';
    document.getElementById('submitBtn').textContent = '添加';
    document.getElementById('accountForm').reset();
    document.getElementById('accountModal').style.display = 'block';
}

// 显示编辑账号模态框
function editAccount(id) {
    const account = accounts.find(a => a.id === id);
    if (!account) return;
    
    editingAccount = account;
    document.getElementById('modalTitle').textContent = '编辑账号';
    document.getElementById('submitBtn').textContent = '保存';
    document.getElementById('username').value = account.username;
    document.getElementById('password').value = account.password;
    document.getElementById('notes').value = account.notes;
    document.getElementById('accountModal').style.display = 'block';
}

// 显示导入模态框
function showImportModal() {
    document.getElementById('importModal').style.display = 'block';
}

// 加载游戏列表
async function loadGameList(selectedGameID) {
    const gameSelect = document.getElementById('gameSelect');

    try {
        addLog('INFO', '正在加载Steam游戏列表...');
        gameSelect.innerHTML = '<option value="">正在加载游戏列表...</option>';

        const games = await GetInstalledSteamGames();
        console.log('获取到的游戏列表:', games);

        // 清空选项
        gameSelect.innerHTML = '';

        if (games && games.length > 0) {
            // 添加游戏选项
            games.forEach(game => {
                const option = document.createElement('option');
                option.value = game.app_id;
                option.textContent = `${game.name} (${game.app_id})`;

                // 如果是当前选择的游戏，设为选中
                if (game.app_id === selectedGameID) {
                    option.selected = true;
                }

                gameSelect.appendChild(option);
            });

            addLog('SUCCESS', `成功加载 ${games.length} 个Steam游戏`);
        } else {
            // 如果没有找到游戏，添加默认选项
            const defaultOption = document.createElement('option');
            defaultOption.value = '578080';
            defaultOption.textContent = 'PLAYERUNKNOWN\'S BATTLEGROUNDS (578080)';
            defaultOption.selected = selectedGameID === '578080';
            gameSelect.appendChild(defaultOption);

            addLog('WARN', '未找到已安装的Steam游戏，使用默认游戏列表');
        }
    } catch (error) {
        console.error('加载游戏列表失败:', error);
        addLog('ERROR', '加载游戏列表失败: ' + error);

        // 出错时显示默认选项
        gameSelect.innerHTML = '';
        const defaultOption = document.createElement('option');
        defaultOption.value = '578080';
        defaultOption.textContent = 'PLAYERUNKNOWN\'S BATTLEGROUNDS (578080)';
        defaultOption.selected = selectedGameID === '578080';
        gameSelect.appendChild(defaultOption);

        showMessage('加载游戏列表失败，使用默认游戏: ' + error, 'warning');
    }
}

// 显示配置模态框
async function showConfigModal() {
    try {
        const config = await GetConfig();
        console.log('获取到的配置:', config); // 调试日志

        // 根据Go结构体的JSON标签访问配置属性
        const autoLaunchGame = config?.steam?.auto_launch_game || false;
        const autoCloseGame = config?.steam?.game_config?.auto_close_game || false;
        const annieEnabled = config?.annie?.enabled || false;
        const annieRarPassword = config?.annie?.rar_password || 'aaa111';
        const currentGameID = config?.steam?.game_config?.game_id || '578080';

        document.getElementById('autoLaunchGame').checked = autoLaunchGame;
        document.getElementById('autoCloseGame').checked = autoCloseGame;
        document.getElementById('annieEnabled').checked = annieEnabled;
        document.getElementById('annieRarPassword').value = annieRarPassword;

        // 加载游戏列表
        await loadGameList(currentGameID);

        // 根据安妮程序启用状态控制密码输入框显示
        const passwordGroup = document.getElementById('anniePasswordGroup');
        if (passwordGroup) {
            passwordGroup.style.display = annieEnabled ? 'block' : 'none';
        }

        document.getElementById('configModal').style.display = 'block';

        addLog('INFO', '配置模态框已打开');
    } catch (error) {
        console.error('获取配置失败:', error);
        addLog('ERROR', '获取配置失败: ' + error);
        showMessage('获取配置失败: ' + error, 'error');
    }
}

// 关闭模态框
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';

    if (modalId === 'accountModal') {
        document.getElementById('accountForm').reset();
        editingAccount = null;
    } else if (modalId === 'importModal') {
        document.getElementById('importText').value = '';
    } else if (modalId === 'steamIdModal') {
        document.getElementById('steamIdInput').value = '';
        settingSteamIdAccountId = null;
    } else if (modalId === 'configModal') {
        // 配置模态框关闭时重置表单
        document.getElementById('annieRarPassword').value = '';
    }
}

// 显示设置SteamID模态框
function setSteamID(accountId) {
    settingSteamIdAccountId = accountId;
    document.getElementById('steamIdModal').style.display = 'block';
    document.getElementById('steamIdInput').focus();
}

// 保存SteamID
async function saveSteamID() {
    const steamId = document.getElementById('steamIdInput').value.trim();

    if (!steamId) {
        showMessage('请输入SteamID', 'error');
        return;
    }

    // 验证SteamID格式
    if (!/^76561\d{12}$/.test(steamId)) {
        showMessage('SteamID格式不正确，应该是17位数字，以76561开头', 'error');
        return;
    }

    try {
        await SetAccountSteamID(settingSteamIdAccountId, steamId);
        showMessage('SteamID设置成功', 'success');
        closeModal('steamIdModal');

        // 刷新账号列表
        setTimeout(() => {
            loadAccounts();
        }, 1000);
    } catch (error) {
        console.error('设置SteamID失败:', error);
        showMessage('设置SteamID失败: ' + error, 'error');
    }
}

// 保存配置
async function saveConfig() {
    const autoLaunchGame = document.getElementById('autoLaunchGame').checked;
    const autoCloseGame = document.getElementById('autoCloseGame').checked;
    const annieEnabled = document.getElementById('annieEnabled').checked;
    const annieRarPassword = document.getElementById('annieRarPassword').value.trim() || 'aaa111';

    // 获取选择的游戏
    const gameSelect = document.getElementById('gameSelect');
    const selectedGameID = gameSelect.value || '578080';
    const selectedGameName = gameSelect.options[gameSelect.selectedIndex]?.text?.split(' (')[0] || 'PLAYERUNKNOWN\'S BATTLEGROUNDS';

    try {
        addLog('INFO', `保存配置: 自动启动游戏=${autoLaunchGame}, 自动关闭游戏=${autoCloseGame}, 启用安妮=${annieEnabled}, RAR密码=${annieRarPassword}, 选择游戏=${selectedGameName}(${selectedGameID})`);
        await UpdateConfig(autoLaunchGame, autoCloseGame, annieEnabled, annieRarPassword, selectedGameID, selectedGameName);
        addLog('SUCCESS', '配置保存成功');
        showMessage('配置保存成功', 'success');
        closeModal('configModal');
    } catch (error) {
        console.error('保存配置失败:', error);
        addLog('ERROR', '保存配置失败: ' + error);
        showMessage('保存配置失败: ' + error, 'error');
    }
}

// 保存账号
async function saveAccount() {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const notes = document.getElementById('notes').value;

    try {
        if (editingAccount) {
            // 编辑模式
            addLog('INFO', `更新账号: ${username}`);
            await UpdateAccount(editingAccount.id, username, password, notes);
            addLog('SUCCESS', `账号 ${username} 更新成功`);
            showMessage('账号更新成功', 'success');
        } else {
            // 添加模式
            addLog('INFO', `添加新账号: ${username}`);
            await AddAccount(username, password, notes);
            addLog('SUCCESS', `账号 ${username} 添加成功，正在后台获取SteamID...`);
            showMessage('账号添加成功，正在后台获取SteamID...', 'success');
        }

        closeModal('accountModal');

        // 如果是添加新账号，强制重新加载以更新分页
        if (!editingAccount) {
            await loadAccounts();
        } else {
            // 如果是编辑现有账号，只需智能刷新
            await refreshAccountsIfChanged();
        }
        loadStats();
    } catch (error) {
        console.error('保存账号失败:', error);
        const operation = editingAccount ? '更新' : '添加';
        addLog('ERROR', `${operation}账号 ${username} 失败: ${error}`);
        showMessage('保存账号失败: ' + error, 'error');
    }
}

// 删除账号
async function deleteAccount(id) {
    if (!confirm('确定要删除这个账号吗？')) {
        return;
    }
    
    try {
        await DeleteAccount(id);
        showMessage('账号删除成功', 'success');
        await refreshAccountsIfChanged();
        loadStats();
    } catch (error) {
        console.error('删除账号失败:', error);
        showMessage('删除账号失败: ' + error, 'error');
    }
}

// 检查是否有账号正在登录中
function isAnyAccountLoggingIn() {
    return accounts.some(account => account.isLoggingIn);
}

// 通过账号ID登录
async function loginAccountById(accountId) {
    const account = accounts.find(acc => acc.id === accountId);
    if (!account) {
        showMessage('账号不存在', 'error');
        return;
    }

    // 检查是否有其他账号正在登录中
    if (isAnyAccountLoggingIn()) {
        showMessage('有账号正在登录中，请等待完成后再试', 'warning');
        return;
    }

    await loginAccount(account.username, account.password, accountId);
}

// 登录账号
async function loginAccount(username, password, accountId) {
    // 检查是否已经在登录中
    const account = accounts.find(acc => acc.id === accountId);
    if (account && (account.isLoggingIn || account.isLoggedIn)) {
        if (account.isLoggedIn) {
            showMessage('该账号已经登录', 'warning');
        } else {
            showMessage('该账号正在登录中，请稍候...', 'warning');
        }
        return;
    }

    try {
        // 设置登录中状态
        setAccountLoggingState(accountId, true);

        addLog('INFO', `开始Steam登录: ${username}`);
        showMessage(`正在启动Steam并登录: ${username}...`, 'info');

        await LoginSteam(username, password);

        addLog('SUCCESS', `Steam登录命令已执行: ${username}`);
        showMessage(`Steam登录命令已执行: ${username}`, 'success');
        showMessage('请检查Steam客户端是否正常启动', 'info');

        // 刷新账号列表以显示登录状态
        setTimeout(async () => {
            setAccountLoggingState(accountId, false);
            await refreshAccountsIfChanged();
        }, 3000);
    } catch (error) {
        console.error('Steam登录失败:', error);
        addLog('ERROR', `Steam登录失败: ${username} - ${error}`);
        showMessage('Steam登录失败: ' + error, 'error');

        // 清除登录中状态
        setAccountLoggingState(accountId, false);
    }
}

// 设置账号登录中状态
function setAccountLoggingState(accountId, isLoggingIn) {
    const account = accounts.find(acc => acc.id === accountId);
    if (account) {
        account.isLoggingIn = isLoggingIn;
        // 重新渲染列表，这会更新所有按钮的状态
        renderAccountList();

        // 添加日志记录状态变化
        if (isLoggingIn) {
            addLog('INFO', `账号 ${account.username} 开始登录，其他账号登录功能已暂时禁用`);
        } else {
            addLog('INFO', `账号 ${account.username} 登录完成，其他账号登录功能已恢复`);
        }
    }
}

// 仅启动游戏
async function launchGameOnly() {
    try {
        showMessage('正在启动游戏...', 'info');
        await LaunchGame();
        showMessage('游戏启动命令已执行', 'success');
    } catch (error) {
        console.error('启动游戏失败:', error);
        showMessage('启动游戏失败: ' + error, 'error');
    }
}

// 导入账号
async function importAccounts() {
    const content = document.getElementById('importText').value;

    if (!content.trim()) {
        showMessage('请输入要导入的账号信息', 'error');
        return;
    }

    try {
        addLog('INFO', '开始批量导入账号...');
        const result = await ImportAccounts(content);

        let message = `导入完成: 成功 ${result.success} 个，失败 ${result.failed} 个`;

        if (result.errors && result.errors.length > 0) {
            addLog('WARN', `导入过程中发现 ${result.errors.length} 个错误`);
            result.errors.forEach(error => {
                addLog('ERROR', error);
            });
        }

        if (result.success > 0) {
            addLog('SUCCESS', `成功导入 ${result.success} 个账号，正在后台获取SteamID...`);
        }

        showMessage(message, result.success > 0 ? 'success' : 'error');
        closeModal('importModal');

        // 批量导入后强制重新加载，因为可能有新账号添加到其他页面
        await loadAccounts();
        loadStats();
    } catch (error) {
        console.error('导入账号失败:', error);
        addLog('ERROR', '导入账号失败: ' + error);
        showMessage('导入账号失败: ' + error, 'error');
    }
}

// 刷新账号列表（手动刷新）
async function refreshAccounts() {
    showMessage('正在刷新账号列表...', 'info');

    // 强制重新加载当前页
    await loadAccounts();
    loadStats();

    showMessage('账号列表已刷新', 'success');
}

// 测试问候功能
async function testGreet() {
    try {
        const result = await Greet('用户');
        showMessage(result, 'info');
    } catch (error) {
        console.error('测试问候失败:', error);
        showMessage('测试问候失败: ' + error, 'error');
    }
}

// 测试高亮功能 - 手动设置第一个账号为登录状态
function testHighlight() {
    if (accounts.length > 0) {
        // 清除所有登录状态
        accounts.forEach(account => account.isLoggedIn = false);
        // 设置第一个账号为登录状态
        accounts[0].isLoggedIn = true;
        console.log('测试：设置账号', accounts[0].username, '为登录状态');
        renderAccountList();
        showMessage('测试高亮效果：已设置第一个账号为登录状态', 'info');
    }
}

// 重试获取SteamID
async function retrySteamID(accountId) {
    const account = accounts.find(acc => acc.id === accountId);
    if (!account) {
        showMessage('账号不存在', 'error');
        return;
    }

    try {
        // 重置状态为获取中
        account.steamId = '';
        renderAccountList();

        addLog('INFO', `重试获取账号 ${account.username} 的SteamID`);
        showMessage(`正在重试获取账号 ${account.username} 的SteamID...`, 'info');

        // 调用后端重新获取SteamID
        await TriggerSteamIDCheck();

        showMessage('已启动SteamID重试获取', 'success');

        // 5秒后检查状态变化
        setTimeout(async () => {
            await refreshAccountsIfChanged();
        }, 5000);
    } catch (error) {
        console.error('重试获取SteamID失败:', error);
        addLog('ERROR', `重试获取SteamID失败: ${error}`);
        showMessage('重试获取SteamID失败: ' + error, 'error');
    }
}

// 显示消息
function showMessage(message, type = 'info') {
    const messageElement = document.createElement('div');
    messageElement.className = `message message-${type}`;
    messageElement.textContent = message;

    document.body.appendChild(messageElement);

    setTimeout(() => {
        if (messageElement.parentNode) {
            messageElement.parentNode.removeChild(messageElement);
        }
    }, 3000);
}

// 日志管理
function addLog(level, message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = {
        timestamp: timestamp,
        level: level,
        message: message,
        source: 'frontend'
    };

    logs.unshift(logEntry); // 新日志添加到开头

    // 保持最多100条日志
    if (logs.length > 100) {
        logs = logs.slice(0, 100);
    }

    // 如果日志区域可见，更新显示
    if (showLogs) {
        renderLogs();
    }
}

// 渲染日志
function renderLogs() {
    const logsContent = document.getElementById('logsContent');

    if (logs.length === 0) {
        logsContent.innerHTML = '<div class="log-empty">暂无日志</div>';
        return;
    }

    const html = logs.map(log => {
        const levelClass = log.level.toLowerCase();
        const icon = getLogIcon(log.level);
        const sourceClass = log.source === 'backend' ? 'log-backend' : 'log-frontend';
        const sourceIcon = log.source === 'backend' ? '🔧' : '💻';
        return `
            <div class="log-entry log-${levelClass} ${sourceClass}">
                <span class="log-time">${log.timestamp}</span>
                <span class="log-level">${icon} ${log.level}</span>
                <span class="log-source" title="${log.source === 'backend' ? '后端日志' : '前端日志'}">${sourceIcon}</span>
                <span class="log-message">${log.message}</span>
            </div>
        `;
    }).join('');

    logsContent.innerHTML = html;
}

// 获取日志图标
function getLogIcon(level) {
    switch (level) {
        case 'SUCCESS': return '✅';
        case 'ERROR': return '❌';
        case 'WARN': return '⚠️';
        case 'INFO': return 'ℹ️';
        default: return 'ℹ️';
    }
}

// 切换日志显示
function toggleLogs() {
    showLogs = !showLogs;
    const logsContainer = document.getElementById('logsContainer');
    const toggleBtn = document.getElementById('logsToggleBtn');

    if (showLogs) {
        logsContainer.style.display = 'block';
        toggleBtn.textContent = '隐藏日志';
        // 立即加载后端日志并渲染
        loadBackendLogs().then(() => {
            renderLogs();
        });
    } else {
        logsContainer.style.display = 'none';
        toggleBtn.textContent = '显示日志';
    }
}

// 清空日志
async function clearLogs() {
    try {
        // 暂停日志轮询，避免立即重新加载
        stopLogPolling();

        // 清空后端日志
        await ClearLogs();

        // 清空前端日志
        logs = [];
        renderLogs();
        showMessage('日志已清空', 'info');

        // 延迟2秒后重新启动日志轮询
        setTimeout(() => {
            startLogPolling();
        }, 2000);

    } catch (error) {
        console.error('清空日志失败:', error);
        // 即使后端清空失败，也清空前端日志
        logs = [];
        renderLogs();
        showMessage('日志已清空', 'info');

        // 重新启动日志轮询
        setTimeout(() => {
            startLogPolling();
        }, 2000);
    }
}

// 启动日志轮询
function startLogPolling() {
    // 立即获取一次日志
    loadBackendLogs();

    // 每3秒轮询一次后端日志
    logPollingInterval = setInterval(loadBackendLogs, 3000);
}

// 停止日志轮询
function stopLogPolling() {
    if (logPollingInterval) {
        clearInterval(logPollingInterval);
        logPollingInterval = null;
    }
}

// 启动账号状态轮询
function startAccountPolling() {
    // 每5秒轮询一次账号状态
    accountPollingInterval = setInterval(refreshAccountsQuietly, 5000);
}

// 停止账号状态轮询
function stopAccountPolling() {
    if (accountPollingInterval) {
        clearInterval(accountPollingInterval);
        accountPollingInterval = null;
    }
}

// 智能刷新账号列表（仅在数据变化时更新）
async function refreshAccountsIfChanged() {
    try {
        // 使用分页API获取当前页的账号
        const response = await GetAccountsPaginated(currentPage, pageSize);
        if (!response || !response.accounts) {
            console.error('获取分页账号数据失败');
            return false;
        }

        const newAccounts = response.accounts;

        // 检查是否有状态变化
        let hasChanges = false;
        if (accounts.length !== newAccounts.length) {
            hasChanges = true;
        } else {
            for (let i = 0; i < accounts.length; i++) {
                const oldAccount = accounts[i];
                const newAccount = newAccounts.find(acc => acc.id === oldAccount.id);

                if (!newAccount) {
                    hasChanges = true;
                    break;
                }

                // 检查关键状态是否变化
                if (oldAccount.steamId !== newAccount.steamId ||
                    oldAccount.status !== newAccount.status ||
                    oldAccount.isLoggedIn !== newAccount.isLoggedIn ||
                    oldAccount.pubgBanStatus !== newAccount.pubgBanStatus ||
                    oldAccount.lastLogin !== newAccount.lastLogin) {
                    hasChanges = true;
                    break;
                }
            }
        }

        // 只有状态变化时才更新界面
        if (hasChanges) {
            accounts = newAccounts;
            totalPages = response.totalPages;
            renderAccountList();
            renderPagination();
            console.log('检测到账号状态变化，界面已更新');
            return true;
        }
        return false;
    } catch (error) {
        console.error('刷新账号失败:', error);
        return false;
    }
}

// 加载后端日志
async function loadBackendLogs() {
    try {
        const backendLogs = await GetLogs();
        if (backendLogs && backendLogs.length > 0) {
            // 合并后端日志和前端日志
            mergeLogs(backendLogs);
        }
    } catch (error) {
        console.error('获取后端日志失败:', error);
        // 不显示错误消息，避免干扰用户
    }
}

// 合并日志
function mergeLogs(backendLogs) {
    // 将后端日志转换为前端日志格式
    const convertedLogs = backendLogs.map(log => ({
        timestamp: log.timestamp,
        level: log.level,
        message: `[${log.category}] ${log.message}`,
        source: 'backend'
    }));

    // 标记现有前端日志
    const frontendLogs = logs.map(log => ({
        ...log,
        source: log.source || 'frontend'
    }));

    // 合并并按时间排序
    const allLogs = [...convertedLogs, ...frontendLogs];

    // 按时间戳排序（新的在前）
    allLogs.sort((a, b) => {
        // 简单的时间比较，假设都是今天的时间
        const timeA = a.timestamp;
        const timeB = b.timestamp;
        return timeB.localeCompare(timeA);
    });

    // 去重（基于时间戳和消息内容）
    const uniqueLogs = [];
    const seen = new Set();

    for (const log of allLogs) {
        const key = `${log.timestamp}-${log.message}`;
        if (!seen.has(key)) {
            seen.add(key);
            uniqueLogs.push(log);
        }
    }

    // 保持最多150条日志
    logs = uniqueLogs.slice(0, 150);

    // 如果日志区域可见，更新显示
    if (showLogs) {
        renderLogs();
    }
}

// 触发SteamID检查
async function triggerSteamIDCheck() {
    try {
        addLog('INFO', '手动触发SteamID检查...');
        showMessage('正在检查缺失的SteamID...', 'info');

        const result = await TriggerSteamIDCheck();

        addLog('SUCCESS', result.message);
        addLog('INFO', `统计: 总账号 ${result.total} 个，需要更新 ${result.needsUpdate} 个，已有SteamID ${result.hasUpdate} 个`);

        showMessage(result.message, result.needsUpdate > 0 ? 'success' : 'info');

        // 5秒后刷新账号列表查看结果
        if (result.needsUpdate > 0) {
            setTimeout(async () => {
                await refreshAccountsIfChanged();
                addLog('INFO', 'SteamID获取任务进行中，请查看账号列表更新');
            }, 5000);
        }
    } catch (error) {
        console.error('触发SteamID检查失败:', error);
        addLog('ERROR', '触发SteamID检查失败: ' + error);
        showMessage('触发SteamID检查失败: ' + error, 'error');
    }
}

// 全局函数，供HTML调用
window.showAddAccountModal = showAddAccountModal;
window.showImportModal = showImportModal;
window.showConfigModal = showConfigModal;
window.closeModal = closeModal;
window.refreshAccounts = refreshAccounts;
window.editAccount = editAccount;
window.deleteAccount = deleteAccount;
window.loginAccount = loginAccount;
window.loginAccountById = loginAccountById;
window.launchGameOnly = launchGameOnly;
window.importAccounts = importAccounts;
window.testGreet = testGreet;
window.changePage = changePage;
window.checkBanStatus = checkBanStatus;
window.setSteamID = setSteamID;
window.saveSteamID = saveSteamID;
window.saveConfig = saveConfig;
window.toggleLogs = toggleLogs;
window.clearLogs = clearLogs;
window.triggerSteamIDCheck = triggerSteamIDCheck;
window.testHighlight = testHighlight;
window.retrySteamID = retrySteamID;

// 页面卸载时停止轮询
window.addEventListener('beforeunload', function() {
    stopLogPolling();
    stopAccountPolling();
});

// 移除了广告相关函数
