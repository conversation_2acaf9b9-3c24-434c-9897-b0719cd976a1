package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"syscall"
	"time"
)

// LoginSteam Steam登录
func (a *App) LoginSteam(username, password string) error {
	// 简单验证
	if username == "" || password == "" {
		return fmt.Errorf("用户名和密码不能为空")
	}

	// 启动Steam登录
	err := a.launchSteamWithAccount(username, password)
	if err != nil {
		return err
	}

	// 更新账号登录状态
	go func() {
		// 等待Steam启动完成
		time.Sleep(3 * time.Second)
		a.updateAccountLastLogin(username)
	}()

	return nil
}

// launchSteamWithAccount 启动Steam并登录指定账号
func (a *App) launchSteamWithAccount(username, password string) error {
	// 如果配置了自动关闭游戏，先关闭游戏
	if a.config.Steam.GameConfig.AutoCloseGame {
		a.addLog("INFO", "正在关闭现有游戏进程...", "general")
		if err := a.closePUBG(); err != nil {
			a.addLog("WARN", fmt.Sprintf("关闭游戏失败: %v", err), "general")
		}
	}

	// 检查Steam是否正在运行
	if a.isSteamRunning() {
		// 如果Steam正在运行，先关闭它
		a.addLog("INFO", "正在关闭现有Steam进程...", "general")
		if err := a.closeSteam(); err != nil {
			a.addLog("WARN", fmt.Sprintf("关闭Steam失败: %v", err), "general")
		}
		// 等待Steam完全关闭
		time.Sleep(3 * time.Second)
	}

	// 启动Steam并登录
	return a.startSteamWithLogin(username, password)
}

// isSteamRunning 检查Steam是否正在运行
func (a *App) isSteamRunning() bool {
	// 使用tasklist命令检查Steam进程
	result, err := a.executeCommand("tasklist", "/FI", "IMAGENAME eq Steam.exe")
	if err != nil {
		return false
	}

	// 检查输出中是否包含Steam.exe
	return strings.Contains(strings.ToLower(result), "steam.exe")
}

// closeSteam 关闭Steam
func (a *App) closeSteam() error {
	// 使用taskkill命令关闭Steam
	cmd := `taskkill /F /IM Steam.exe`
	_, err := a.executeCommand("cmd", "/C", cmd)
	return err
}

// isGameRunning 检查指定游戏进程是否正在运行
func (a *App) isGameRunning(processName string) bool {
	result, err := a.executeCommand("tasklist", "/FI", fmt.Sprintf("IMAGENAME eq %s", processName))
	if err != nil {
		return false
	}
	// 检查输出中是否包含进程名
	return strings.Contains(strings.ToLower(result), strings.ToLower(processName))
}

// isPUBGRunning 检查PUBG是否正在运行
func (a *App) isPUBGRunning() bool {
	// PUBG的可能进程名
	pubgProcesses := []string{
		"TslGame.exe",
		"TslGame_BE.exe",
		"PUBG.exe",
		"ExecPubg.exe",
	}

	for _, processName := range pubgProcesses {
		if a.isGameRunning(processName) {
			return true
		}
	}
	return false
}

// closeGame 关闭指定的游戏进程
func (a *App) closeGame(processName string) error {
	if !a.isGameRunning(processName) {
		return nil // 游戏没有运行，无需关闭
	}

	// 尝试优雅关闭
	cmd := fmt.Sprintf(`taskkill /IM %s`, processName)
	_, err := a.executeCommand("cmd", "/C", cmd)
	if err == nil {
		// 等待进程关闭
		time.Sleep(3 * time.Second)
		if !a.isGameRunning(processName) {
			return nil
		}
	}

	// 强制关闭
	cmd = fmt.Sprintf(`taskkill /F /IM %s`, processName)
	_, err = a.executeCommand("cmd", "/C", cmd)
	return err
}

// closePUBG 关闭PUBG游戏
func (a *App) closePUBG() error {
	// PUBG的可能进程名
	pubgProcesses := []string{
		"TslGame.exe",
		"TslGame_BE.exe",
		"PUBG.exe",
		"ExecPubg.exe",
	}

	var lastErr error
	for _, processName := range pubgProcesses {
		err := a.closeGame(processName)
		if err != nil {
			lastErr = err
			a.addLog("ERROR", fmt.Sprintf("关闭 %s 失败: %v", processName, err), "general")
		} else if a.isGameRunning(processName) {
			a.addLog("SUCCESS", fmt.Sprintf("成功关闭 %s", processName), "general")
		}
	}

	return lastErr
}

// startSteamWithLogin 启动Steam并登录
func (a *App) startSteamWithLogin(username, password string) error {
	// 查找Steam安装路径
	steamPath := a.findSteamPath()
	if steamPath == "" {
		return fmt.Errorf("未找到Steam安装路径")
	}

	// 构建Steam启动命令
	steamExe := filepath.Join(steamPath, "Steam.exe")

	// 启动Steam（不等待完成，隐藏窗口）
	cmd := exec.Command(steamExe, "-login", username, password)

	// 在Windows上隐藏命令行窗口
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: 0x08000000, // CREATE_NO_WINDOW
	}

	err := cmd.Start()
	if err != nil {
		a.addLog("ERROR", fmt.Sprintf("启动Steam失败: %v", err), "general")
		return fmt.Errorf("启动Steam失败: %v", err)
	}

	a.addLog("SUCCESS", fmt.Sprintf("Steam启动命令已执行: %s -login %s ***", steamExe, username), "general")

	// 等待Steam启动
	time.Sleep(5 * time.Second)

	// 如果配置了自动启动游戏，则启动游戏
	if a.config.Steam.AutoLaunchGame {
		go func() {
			// 等待Steam完全启动
			time.Sleep(10 * time.Second)

			// 如果启用了安妮程序，先启动安妮程序
			annieSuccess := false
			if a.config.Annie.Enabled {
				a.addLog("INFO", "Steam登录成功，开始启动安妮程序", "general")
				if err := a.launchAnnie(); err != nil {
					a.addLog("ERROR", fmt.Sprintf("启动安妮程序失败: %v", err), "general")
					a.addLog("INFO", "安妮程序启动失败，跳过游戏启动", "general")
					return // 安妮程序启动失败，不启动游戏
				} else {
					// 安妮程序启动成功，等待其处理完成后再启动游戏
					a.addLog("INFO", "安妮程序启动成功，等待处理完成后启动游戏", "general")
					// 等待并处理弹窗、进程退出和文件消失
					if err := a.handleAnnieDialogs(); err != nil {
						a.addLog("ERROR", fmt.Sprintf("安妮程序处理失败: %v", err), "general")
						a.addLog("INFO", "安妮程序处理失败，跳过游戏启动", "general")
						return // 安妮程序处理失败，不启动游戏
					}
					annieSuccess = true
				}
			}

			// 只有在安妮程序未启用或处理完成后才启动游戏
			if !a.config.Annie.Enabled || annieSuccess {
				if err := a.launchGame(); err != nil {
					a.addLog("ERROR", fmt.Sprintf("启动游戏失败: %v", err), "general")
				}
			}
		}()
	}

	return nil
}

// findSteamPath 查找Steam安装路径
func (a *App) findSteamPath() string {
	// 常见的Steam安装路径
	commonPaths := []string{
		`C:\Program Files (x86)\Steam`,
		`C:\Program Files\Steam`,
		`D:\Steam`,
		`E:\Steam`,
		`F:\Steam`,
	}

	// 检查常见路径
	for _, path := range commonPaths {
		steamExe := filepath.Join(path, "Steam.exe")
		if _, err := os.Stat(steamExe); err == nil {
			return path
		}
	}

	// 尝试从注册表读取
	steamPath := a.getSteamPathFromRegistry()
	if steamPath != "" {
		return steamPath
	}

	return ""
}

// getSteamPathFromRegistry 从注册表获取Steam路径
func (a *App) getSteamPathFromRegistry() string {
	// 使用reg命令查询注册表
	cmd := `reg query "HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Valve\Steam" /v InstallPath 2>NUL`
	result, err := a.executeCommand("cmd", "/C", cmd)
	if err != nil {
		return ""
	}

	// 解析注册表输出
	lines := strings.Split(result, "\n")
	for _, line := range lines {
		if strings.Contains(line, "InstallPath") && strings.Contains(line, "REG_SZ") {
			parts := strings.Fields(line)
			if len(parts) >= 3 {
				return strings.Join(parts[2:], " ")
			}
		}
	}

	return ""
}

// launchGame 启动游戏
func (a *App) launchGame() error {
	gameID := a.config.Steam.GameConfig.GameID
	if gameID == "" {
		gameID = "578080" // PUBG的Steam ID
	}

	// 使用Steam URL协议启动游戏
	steamURL := fmt.Sprintf("steam://rungameid/%s", gameID)

	// 使用start命令启动URL（隐藏窗口）
	cmd := exec.Command("cmd", "/C", "start", steamURL)

	// 在Windows上隐藏命令行窗口
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: 0x08000000, // CREATE_NO_WINDOW
	}

	err := cmd.Start()
	if err != nil {
		a.addLog("ERROR", fmt.Sprintf("启动游戏失败: %v", err), "general")
		return err
	}

	a.addLog("SUCCESS", fmt.Sprintf("游戏启动命令已执行: %s", steamURL), "general")
	return nil
}

// LaunchGame 公开的启动游戏方法
func (a *App) LaunchGame() error {
	return a.launchGame()
}

// GetInstalledSteamGames 获取本地已安装的Steam游戏列表
func (a *App) GetInstalledSteamGames() ([]SteamGame, error) {
	steamPath := a.findSteamPath()
	if steamPath == "" {
		a.addLog("ERROR", "未找到Steam安装路径", "general")
		return nil, fmt.Errorf("未找到Steam安装路径")
	}

	a.addLog("INFO", fmt.Sprintf("找到Steam安装路径: %s", steamPath), "general")
	games := []SteamGame{}

	// 添加默认的PUBG游戏
	games = append(games, SteamGame{
		AppID: "578080",
		Name:  "绝地求生",
	})

	// 获取所有Steam库路径
	libraryPaths := a.getSteamLibraryPaths(steamPath)
	a.addLog("INFO", fmt.Sprintf("找到 %d 个Steam库路径", len(libraryPaths)), "general")

	for _, libraryPath := range libraryPaths {
		a.addLog("INFO", fmt.Sprintf("扫描Steam库: %s", libraryPath), "general")
		libraryGames := a.scanSteamLibrary(libraryPath)

		for _, game := range libraryGames {
			// 过滤掉一些非游戏的应用（如Steam工具、DLC等）
			if a.isValidGame(game) {
				games = append(games, game)
				a.addLog("INFO", fmt.Sprintf("添加有效游戏: %s (%s)", game.Name, game.AppID), "general")
			} else {
				a.addLog("INFO", fmt.Sprintf("过滤掉非游戏应用: %s (%s)", game.Name, game.AppID), "general")
			}
		}
	}

	a.addLog("INFO", fmt.Sprintf("最终找到 %d 个已安装的Steam游戏", len(games)), "general")
	return games, nil
}

// parseACFFile 解析ACF文件获取游戏信息
func (a *App) parseACFFile(filePath string) (SteamGame, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return SteamGame{}, err
	}

	contentStr := string(content)
	game := SteamGame{}

	// 使用正则表达式或更简单的字符串解析
	lines := strings.Split(contentStr, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 解析AppID
		if strings.Contains(line, `"appid"`) && strings.Contains(line, `"`) {
			parts := strings.Split(line, `"`)
			if len(parts) >= 4 {
				game.AppID = parts[3]
			}
		}

		// 解析游戏名称
		if strings.Contains(line, `"name"`) && strings.Contains(line, `"`) {
			parts := strings.Split(line, `"`)
			if len(parts) >= 4 {
				game.Name = parts[3]
			}
		}

		// 如果两个都找到了，可以提前退出
		if game.AppID != "" && game.Name != "" {
			break
		}
	}

	if game.AppID == "" || game.Name == "" {
		return SteamGame{}, fmt.Errorf("无法解析游戏信息: AppID=%s, Name=%s", game.AppID, game.Name)
	}

	// 尝试获取中文名称
	chineseName := a.getChineseGameName(game.AppID, game.Name)
	if chineseName != "" {
		game.Name = chineseName
	}

	return game, nil
}

// extractValueFromACF 从ACF内容中提取值
func (a *App) extractValueFromACF(content string) string {
	lines := strings.Split(content, "\n")
	if len(lines) == 0 {
		return ""
	}

	// 找到包含值的行
	firstLine := strings.TrimSpace(lines[0])
	if strings.Contains(firstLine, `"`) {
		// 提取引号中的值
		parts := strings.Split(firstLine, `"`)
		if len(parts) >= 4 {
			return parts[3] // 第二个引号对中的内容
		}
	}

	return ""
}

// isValidGame 判断是否为有效的游戏（过滤工具、DLC等）
func (a *App) isValidGame(game SteamGame) bool {
	// 过滤掉一些明显的非游戏应用
	excludeKeywords := []string{
		"Steamworks Common Redistributables",
		"Steam Client",
		"Proton",
		"DirectX",
		"Visual C++",
		"Microsoft",
	}

	gameName := strings.ToLower(game.Name)
	for _, keyword := range excludeKeywords {
		if strings.Contains(gameName, strings.ToLower(keyword)) {
			a.addLog("INFO", fmt.Sprintf("过滤关键词匹配: %s 包含 %s", game.Name, keyword), "general")
			return false
		}
	}

	// AppID过滤：一些特殊的AppID范围通常不是游戏
	// Steam工具类应用通常AppID较小
	if game.AppID == "228980" || // Steamworks Common Redistributables
		game.AppID == "1007" || // Steam Client Bootstrapper
		game.AppID == "7" { // Steam Client
		a.addLog("INFO", fmt.Sprintf("过滤特殊AppID: %s (%s)", game.Name, game.AppID), "general")
		return false
	}

	// 跳过重复的PUBG（因为我们已经默认添加了）
	if game.AppID == "578080" {
		a.addLog("INFO", fmt.Sprintf("跳过重复的PUBG: %s (%s)", game.Name, game.AppID), "general")
		return false
	}

	return true
}

// getSteamLibraryPaths 获取所有Steam库路径
func (a *App) getSteamLibraryPaths(steamPath string) []string {
	var libraryPaths []string

	// 默认添加主Steam库
	mainLibrary := filepath.Join(steamPath, "steamapps")
	libraryPaths = append(libraryPaths, mainLibrary)

	// 读取libraryfolders.vdf文件获取其他库路径
	vdfPath := filepath.Join(steamPath, "steamapps", "libraryfolders.vdf")
	a.addLog("INFO", fmt.Sprintf("尝试读取库配置文件: %s", vdfPath), "general")

	if _, err := os.Stat(vdfPath); os.IsNotExist(err) {
		a.addLog("WARN", fmt.Sprintf("库配置文件不存在: %s", vdfPath), "general")
		return libraryPaths
	}

	content, err := os.ReadFile(vdfPath)
	if err != nil {
		a.addLog("WARN", fmt.Sprintf("读取库配置文件失败: %v", err), "general")
		return libraryPaths
	}

	// 解析VDF文件获取库路径
	contentStr := string(content)
	lines := strings.Split(contentStr, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		// 查找路径行，格式类似: "path"		"D:\\SteamLibrary"
		if strings.Contains(line, `"path"`) && strings.Count(line, `"`) >= 4 {
			parts := strings.Split(line, `"`)
			if len(parts) >= 4 {
				libPath := parts[3]
				// 处理路径中的双反斜杠
				libPath = strings.ReplaceAll(libPath, `\\`, `\`)
				steamappsPath := filepath.Join(libPath, "steamapps")

				// 检查路径是否存在且不重复
				if _, err := os.Stat(steamappsPath); err == nil {
					// 检查是否已存在
					exists := false
					for _, existing := range libraryPaths {
						if existing == steamappsPath {
							exists = true
							break
						}
					}
					if !exists {
						libraryPaths = append(libraryPaths, steamappsPath)
						a.addLog("INFO", fmt.Sprintf("找到额外Steam库: %s", steamappsPath), "general")
					}
				}
			}
		}
	}

	return libraryPaths
}

// scanSteamLibrary 扫描单个Steam库
func (a *App) scanSteamLibrary(libraryPath string) []SteamGame {
	var games []SteamGame

	// 检查steamapps目录是否存在
	if _, err := os.Stat(libraryPath); os.IsNotExist(err) {
		a.addLog("WARN", fmt.Sprintf("Steam库目录不存在: %s", libraryPath), "general")
		return games
	}

	// 读取所有.acf文件（Steam应用缓存文件）
	acfFiles, err := filepath.Glob(filepath.Join(libraryPath, "appmanifest_*.acf"))
	if err != nil {
		a.addLog("WARN", fmt.Sprintf("读取Steam库游戏列表失败: %s - %v", libraryPath, err), "general")
		return games
	}

	a.addLog("INFO", fmt.Sprintf("在库 %s 中找到 %d 个ACF文件", libraryPath, len(acfFiles)), "general")

	for _, acfFile := range acfFiles {
		a.addLog("INFO", fmt.Sprintf("解析ACF文件: %s", acfFile), "general")
		game, err := a.parseACFFile(acfFile)
		if err != nil {
			a.addLog("WARN", fmt.Sprintf("解析ACF文件失败: %s - %v", acfFile, err), "general")
			continue // 跳过解析失败的文件
		}

		a.addLog("INFO", fmt.Sprintf("解析到游戏: %s (%s)", game.Name, game.AppID), "general")
		games = append(games, game)
	}

	return games
}

// getChineseGameName 获取游戏的中文名称
func (a *App) getChineseGameName(appID, englishName string) string {
	// 首先尝试从内置的游戏名称映射获取（最高优先级，确保官方中文名称）
	chineseName := a.getGameNameFromMapping(appID)
	if chineseName != "" {
		a.addLog("INFO", fmt.Sprintf("使用内置映射获取游戏名称: %s -> %s", appID, chineseName), "general")
		return chineseName
	}

	// 如果没有找到映射，尝试从Steam Store API获取中文名称
	//chineseName = a.getGameNameFromSteamStoreAPI(appID)
	//if chineseName != "" {
	//	a.addLog("SUCCESS", fmt.Sprintf("从Steam Store API获取游戏名称: %s -> %s", appID, chineseName), "general")
	//	return chineseName
	//}

	// 如果Steam Store API也失败，尝试从Steam本地缓存获取
	chineseName = a.getGameNameFromSteamCache(appID)
	if chineseName != "" {
		a.addLog("SUCCESS", fmt.Sprintf("从Steam本地缓存获取游戏名称: %s -> %s", appID, chineseName), "general")
		return chineseName
	}

	// 都没找到，返回英文名称
	a.addLog("WARN", fmt.Sprintf("无法获取游戏 %s 的中文名称，使用英文名称: %s", appID, englishName), "general")
	return englishName
}

// getGameNameFromMapping 从内置映射获取游戏中文名称
func (a *App) getGameNameFromMapping(appID string) string {
	// 常见游戏的中文名称映射
	gameNameMap := map[string]string{
		"578080":  "绝地求生",
		"730":     "反恐精英：全球攻势",
		"440":     "军团要塞2",
		"570":     "Dota 2",
		"271590":  "侠盗猎车手V",
		"292030":  "巫师3：狂猎",
		"431960":  "壁纸引擎",
		"1172470": "光环：无限",
		"1245620": "艾尔登法环",
		"1086940": "博德之门3",
		"1174180": "荒野大镖客：救赎2",
		"1091500": "赛博朋克2077",
		"1203220": "永劫无间",
		"1517290": "战地2042",
		"1938090": "使命召唤：现代战争III",
		"1966720": "暗黑破坏神4",
		"1623730": "幻兽帕鲁",
		"1888930": "地平线：西部禁域",
		"1817070": "漫威蜘蛛侠：重制版",
		"1817190": "漫威蜘蛛侠：迈尔斯·莫拉莱斯",
		"1237970": "钛陨落2",
		"1172380": "星球大战绝地：陨落的武士团",
		"1551360": "觅长生",
		"1449850": "游戏王：决斗大师",
		"1794680": "吸血鬼幸存者",
		"1675200": "最后的咒语",
		"1593500": "上古卷轴5：天际特别版",
		"489830":  "上古卷轴5：天际",
		"1085660": "命运2",
		"1222670": "生化危机2：重制版",
		"1196590": "生化危机3：重制版",
		"1196240": "生化危机7：生化危机",
		"1463930": "生化危机4：重制版",
		"381210":  "死亡搁浅",
		"1151640": "地平线：零之曙光",
		"1237950": "星球大战绝地：陨落的武士团",
		"1144200": "生化危机：抵抗",
		"4000":    "Garry's Mod",
		"252490":  "Rust",
		"322330":  "饥荒：联机版",
		"304930":  "Unturned",
		"251570":  "七日杀",
		"346110":  "方舟：生存进化",
		"394360":  "钢铁雄心4",
		"281990":  "群星",
		"236850":  "欧陆风云4",
		"203770":  "十字军之王2",
		"1158310": "十字军之王3",
		"1097840": "几何冲刺",
		"105600":  "泰拉瑞亚",
		"413150":  "星露谷物语",
		"367520":  "空洞骑士",
		"646570":  "杀戮尖塔",
		"1145360": "哈迪斯",
		"582010":  "怪物猎人：世界",
		"1118010": "怪物猎人：崛起",
	}

	return gameNameMap[appID]
}

// SteamStoreAPIResponse Steam Store API响应结构
type SteamStoreAPIResponse map[string]SteamStoreAppData

// SteamStoreAppData Steam Store应用数据
type SteamStoreAppData struct {
	Success bool                `json:"success"`
	Data    SteamStoreAppDetail `json:"data"`
}

// SteamStoreAppDetail Steam Store应用详情
type SteamStoreAppDetail struct {
	Type       string   `json:"type"`
	Name       string   `json:"name"`
	SteamAppID int      `json:"steam_appid"`
	IsFree     bool     `json:"is_free"`
	Developers []string `json:"developers"`
	Publishers []string `json:"publishers"`
}

// getGameNameFromSteamStoreAPI 从Steam Store API获取游戏中文名称
func (a *App) getGameNameFromSteamStoreAPI(appID string) string {
	// 构建Steam Store API URL，使用中文本地化参数
	// l=schinese: 简体中文
	// cc=cn: 中国地区
	url := fmt.Sprintf("https://store.steampowered.com/api/appdetails?appids=%s&l=schinese&cc=cn", appID)

	a.addLog("INFO", fmt.Sprintf("正在从Steam Store API获取游戏名称: %s", appID), "general")

	// 创建HTTP客户端，设置超时
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// 发送HTTP请求
	resp, err := client.Get(url)
	if err != nil {
		a.addLog("WARN", fmt.Sprintf("Steam Store API请求失败: %v", err), "general")
		return ""
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		a.addLog("WARN", fmt.Sprintf("Steam Store API返回错误状态码: %d", resp.StatusCode), "general")
		return ""
	}

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		a.addLog("WARN", fmt.Sprintf("读取Steam Store API响应失败: %v", err), "general")
		return ""
	}

	// 解析JSON响应
	var apiResponse SteamStoreAPIResponse
	if err := json.Unmarshal(body, &apiResponse); err != nil {
		a.addLog("WARN", fmt.Sprintf("解析Steam Store API响应失败: %v", err), "general")
		return ""
	}

	// 获取应用数据
	appData, exists := apiResponse[appID]
	if !exists {
		a.addLog("WARN", fmt.Sprintf("Steam Store API响应中未找到应用: %s", appID), "general")
		return ""
	}

	// 检查请求是否成功
	if !appData.Success {
		a.addLog("WARN", fmt.Sprintf("Steam Store API返回失败状态: %s", appID), "general")
		return ""
	}

	// 检查是否为游戏类型
	if appData.Data.Type != "game" {
		a.addLog("INFO", fmt.Sprintf("应用 %s 不是游戏类型: %s", appID, appData.Data.Type), "general")
		return ""
	}

	// 返回游戏名称
	if appData.Data.Name != "" {
		a.addLog("SUCCESS", fmt.Sprintf("Steam Store API成功获取游戏名称: %s -> %s", appID, appData.Data.Name), "general")
		return appData.Data.Name
	}

	a.addLog("WARN", fmt.Sprintf("Steam Store API返回空的游戏名称: %s", appID), "general")
	return ""
}

// getGameNameFromSteamCache 从Steam本地缓存获取游戏名称
func (a *App) getGameNameFromSteamCache(appID string) string {
	steamPath := a.findSteamPath()
	if steamPath == "" {
		a.addLog("WARN", fmt.Sprintf("无法获取Steam路径，跳过缓存查找: %s", appID), "general")
		return ""
	}

	a.addLog("INFO", fmt.Sprintf("开始从Steam缓存查找游戏名称: %s", appID), "general")

	// 尝试从多个可能的位置获取游戏名称
	gameName := a.getGameNameFromLocalConfig(steamPath, appID)
	if gameName != "" {
		a.addLog("SUCCESS", fmt.Sprintf("从本地配置获取到游戏名称: %s -> %s", appID, gameName), "general")
		return gameName
	}

	// 尝试从Steam的本地化文件获取
	gameName = a.getGameNameFromLocalization(steamPath, appID)
	if gameName != "" {
		a.addLog("SUCCESS", fmt.Sprintf("从本地化文件获取到游戏名称: %s -> %s", appID, gameName), "general")
		return gameName
	}

	// 尝试从注册表获取
	gameName = a.getGameNameFromRegistry(appID)
	if gameName != "" {
		a.addLog("SUCCESS", fmt.Sprintf("从注册表获取到游戏名称: %s -> %s", appID, gameName), "general")
		return gameName
	}

	a.addLog("INFO", fmt.Sprintf("未能从Steam缓存找到游戏名称: %s", appID), "general")
	return ""
}

// getGameNameFromLocalConfig 从Steam本地配置获取游戏名称
func (a *App) getGameNameFromLocalConfig(steamPath, appID string) string {
	// 尝试从localconfig.vdf文件获取游戏名称
	configPath := filepath.Join(steamPath, "userdata")
	a.addLog("INFO", fmt.Sprintf("查找用户配置目录: %s", configPath), "general")

	// 遍历用户目录
	userDirs, err := os.ReadDir(configPath)
	if err != nil {
		a.addLog("WARN", fmt.Sprintf("无法读取用户配置目录: %v", err), "general")
		return ""
	}

	a.addLog("INFO", fmt.Sprintf("找到 %d 个用户目录", len(userDirs)), "general")

	for _, userDir := range userDirs {
		if !userDir.IsDir() {
			continue
		}

		localConfigPath := filepath.Join(configPath, userDir.Name(), "config", "localconfig.vdf")
		a.addLog("INFO", fmt.Sprintf("检查配置文件: %s", localConfigPath), "general")

		if _, err := os.Stat(localConfigPath); os.IsNotExist(err) {
			a.addLog("INFO", fmt.Sprintf("配置文件不存在: %s", localConfigPath), "general")
			continue
		}

		gameName := a.parseLocalConfigForGameName(localConfigPath, appID)
		if gameName != "" {
			a.addLog("SUCCESS", fmt.Sprintf("从localconfig.vdf获取游戏名称: %s -> %s", appID, gameName), "general")
			return gameName
		}
	}

	a.addLog("INFO", fmt.Sprintf("未从本地配置找到游戏名称: %s", appID), "general")
	return ""
}

// parseLocalConfigForGameName 解析localconfig.vdf文件获取游戏名称
func (a *App) parseLocalConfigForGameName(filePath, appID string) string {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return ""
	}

	contentStr := string(content)

	// 查找对应AppID的游戏信息
	// localconfig.vdf中的格式通常是：
	// "Apps"
	// {
	//     "578080"
	//     {
	//         "name"    "PLAYERUNKNOWN'S BATTLEGROUNDS"
	//         ...
	//     }
	// }

	appIDIndex := strings.Index(contentStr, `"`+appID+`"`)
	if appIDIndex == -1 {
		return ""
	}

	// 从AppID位置开始查找name字段
	searchArea := contentStr[appIDIndex:]
	nameIndex := strings.Index(searchArea, `"name"`)
	if nameIndex == -1 {
		return ""
	}

	// 提取name值
	nameArea := searchArea[nameIndex:]
	lines := strings.Split(nameArea, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, `"name"`) && strings.Count(line, `"`) >= 4 {
			parts := strings.Split(line, `"`)
			if len(parts) >= 4 {
				return parts[3]
			}
		}
	}

	return ""
}

// getGameNameFromLocalization 从Steam本地化文件获取游戏名称
func (a *App) getGameNameFromLocalization(steamPath, appID string) string {
	// Steam的本地化文件通常在以下位置：
	// Steam\resource\localization\schinese\steam_schinese.txt
	// Steam\resource\localization\tchinese\steam_tchinese.txt

	localizationPaths := []string{
		filepath.Join(steamPath, "resource", "localization", "schinese", "steam_schinese.txt"),
		filepath.Join(steamPath, "resource", "localization", "tchinese", "steam_tchinese.txt"),
		filepath.Join(steamPath, "resource", "localization", "chinese", "steam_chinese.txt"),
	}

	for _, locPath := range localizationPaths {
		if _, err := os.Stat(locPath); os.IsNotExist(err) {
			continue
		}

		gameName := a.parseLocalizationFile(locPath, appID)
		if gameName != "" {
			a.addLog("INFO", fmt.Sprintf("从本地化文件获取游戏名称: %s -> %s", appID, gameName), "general")
			return gameName
		}
	}

	return ""
}

// parseLocalizationFile 解析本地化文件获取游戏名称
func (a *App) parseLocalizationFile(filePath, appID string) string {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return ""
	}

	contentStr := string(content)
	lines := strings.Split(contentStr, "\n")

	// 查找格式如：AppName_578080    "绝地求生"
	searchKey := "AppName_" + appID
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, searchKey) && strings.Contains(line, `"`) {
			// 提取引号中的内容
			parts := strings.Split(line, `"`)
			if len(parts) >= 2 {
				return parts[1]
			}
		}
	}

	return ""
}

// getGameNameFromRegistry 从注册表获取游戏名称
func (a *App) getGameNameFromRegistry(appID string) string {
	// 尝试从Steam注册表项获取游戏名称
	// HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Steam App {appID}
	regPath := fmt.Sprintf(`HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Steam App %s`, appID)
	a.addLog("INFO", fmt.Sprintf("查询注册表路径: %s", regPath), "general")

	result, err := a.executeCommand("reg", "query", regPath, "/v", "DisplayName")
	if err != nil {
		a.addLog("INFO", fmt.Sprintf("注册表查询失败: %v", err), "general")
		return ""
	}

	// 解析注册表输出
	lines := strings.Split(result, "\n")
	for _, line := range lines {
		if strings.Contains(line, "DisplayName") && strings.Contains(line, "REG_SZ") {
			parts := strings.Fields(line)
			if len(parts) >= 3 {
				displayName := strings.Join(parts[2:], " ")
				a.addLog("SUCCESS", fmt.Sprintf("从注册表获取游戏名称: %s -> %s", appID, displayName), "general")
				return displayName
			}
		}
	}

	a.addLog("INFO", fmt.Sprintf("注册表中未找到游戏名称: %s", appID), "general")
	return ""
}
