package main

import (
	"context"
	"database/sql"
	"log"
	"sync"
	"time"

	_ "modernc.org/sqlite"
)

// App struct
type App struct {
	ctx         context.Context
	db          *sql.DB
	config      *Config
	accounts    []Account
	mutex       sync.RWMutex
	nextID      int
	apiKeyIndex int
	logs        []LogEntry
	logMutex    sync.RWMutex
	gameNameCache map[string]string // 游戏名称缓存，避免重复API调用
	gameNameMutex sync.RWMutex      // 游戏名称缓存的读写锁
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{
		accounts:      make([]Account, 0),
		nextID:        1,
		logs:          make([]LogEntry, 0),
		gameNameCache: make(map[string]string),
	}
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx

	// 初始化配置
	if err := a.initializeConfig(); err != nil {
		log.Printf("Failed to initialize config: %v", err)
	}

	// 初始化数据库
	if err := a.initializeDatabase(); err != nil {
		log.Printf("Failed to initialize database: %v", err)
		// 如果数据库初始化失败，使用内存存储
		a.accounts = []Account{
			{ID: 1, Username: "demo1", Password: "password1", Notes: "测试账号1", Status: "正常", IsLoggedIn: false},
			{ID: 2, Username: "demo2", Password: "password2", Notes: "测试账号2", Status: "未知", IsLoggedIn: false},
		}
		a.nextID = 3
	} else {
		// 从数据库加载账号
		a.loadAccountsFromDB()
	}

	// 启动后台任务
	go a.startBackgroundTasks()

	// 启动时检查并获取缺失的SteamID，完成后检测封禁状态
	go a.startupSteamIDAndBanCheck()
}

// startBackgroundTasks 启动后台任务
func (a *App) startBackgroundTasks() {
	// 每30分钟检查一次缺失的SteamID
	steamIDTicker := time.NewTicker(30 * time.Minute)
	defer steamIDTicker.Stop()

	// 每1小时检查一次封禁状态（检查1小时内未检测的账号）
	banCheckTicker := time.NewTicker(1 * time.Hour)
	defer banCheckTicker.Stop()

	for {
		select {
		case <-steamIDTicker.C:
			go a.checkAndUpdateMissingSteamIDs()
		case <-banCheckTicker.C:
			a.addLog("INFO", "定时任务：开始检查1小时内未检测的账号封禁状态", "scheduler")
			go a.checkBanStatusBackground()
		case <-a.ctx.Done():
			return
		}
	}
}

// startupSteamIDAndBanCheck 启动时获取SteamID并检测封禁状态
func (a *App) startupSteamIDAndBanCheck() {
	a.addLog("INFO", "程序启动：开始检查缺失的SteamID", "startup")

	// 先获取缺失的SteamID
	a.checkAndUpdateMissingSteamIDs()

	// 等待SteamID获取完成（给一些时间让SteamID获取任务完成）
	time.Sleep(5 * time.Second)

	// 检查是否有Steam API密钥配置
	if len(a.config.API.SteamAPIKeys) == 0 {
		a.addLog("WARN", "未配置Steam API密钥，跳过封禁状态检测", "startup")
		return
	}

	a.addLog("INFO", "SteamID获取完成，开始检测1小时内未检测的账号封禁状态", "startup")

	// 启动封禁状态检测
	a.checkBanStatusOnStartup()
}
